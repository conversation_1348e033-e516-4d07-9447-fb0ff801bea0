import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';

// 获取供应商列表
export async function GET() {
  try {
    const 供应商 = await query('SELECT * FROM 供应商 ORDER BY created_at DESC');
    return NextResponse.json({ success: true, data: 供应商 });
  } catch (error) {
    console.error('获取供应商列表失败:', error);
    return NextResponse.json(
      { success: false, message: '获取供应商列表失败' },
      { status: 500 }
    );
  }
}

// 创建新供应商
export async function POST(request: NextRequest) {
  try {
    const db = await getDatabase();
    const data = await request.json();
    
    const result = await db.run(
      `INSERT INTO 供应商 (
        name, contact_person, phone, email, address,
        tax_number, bank_account, bank_name, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        data.name,
        data.contact_person,
        data.phone,
        data.email,
        data.address,
        data.tax_number,
        data.bank_account,
        data.bank_name,
        data.status || 'active'
      ]
    );

    return NextResponse.json({
      success: true,
      data: { id: result.lastID }
    });
  } catch (error) {
    console.error('添加供应商失败:', error);
    return NextResponse.json(
      { success: false, message: '添加供应商失败' },
      { status: 500 }
    );
  }
}

// 更新供应商信息
export async function PUT(request: NextRequest) {
  try {
    const data = await request.json();
    const {
      id,
      name,
      contact_person,
      phone,
      email,
      address,
      tax_number,
      bank_name,
      bank_account,
      license_number,
      license_expiry_date,
      gsp_certificate,
      gsp_expiry_date,
      business_scope,
      quality_officer,
      status,
      notes
    } = data;

    const db = await getDatabase();
    await db.run(
      `UPDATE 供应商
      SET
        name = ?,
        contact_person = ?,
        phone = ?,
        email = ?,
        address = ?,
        tax_number = ?,
        bank_name = ?,
        bank_account = ?,
        license_number = ?,
        license_expiry_date = ?,
        gsp_certificate = ?,
        gsp_expiry_date = ?,
        business_scope = ?,
        quality_officer = ?,
        status = ?,
        notes = ?,
        updated_at = CURRENT_TIMESTAMP
      WHERE 编号 = ?`,
      [name, contact_person, phone, email, address,
       tax_number, bank_name, bank_account, license_number,
       license_expiry_date, gsp_certificate, gsp_expiry_date,
       business_scope, quality_officer, status, notes, id]
    );

    const updatedSupplier = await db.get(
      'SELECT * FROM 供应商 WHERE 编号 = ?',
      [id]
    );

    if (!updatedSupplier) {
      return NextResponse.json(
        { success: false, message: '供应商不存在' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: updatedSupplier
    });
  } catch (error) {
    console.error('更新供应商失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: '更新供应商失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// 删除供应商
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { success: false, message: '供应商ID不能为空' },
        { status: 400 }
      );
    }

    // 检查供应商是否存在
    const db = await getDatabase();
    const checkResult = await db.get(
      'SELECT id FROM 供应商 WHERE 编号 = ?',
      [id]
    );

    if (!checkResult) {
      return NextResponse.json(
        { success: false, message: '供应商不存在' },
        { status: 404 }
      );
    }

    // 删除供应商
    await db.run('DELETE FROM 供应商 WHERE 编号 = ?', [id]);

    return NextResponse.json({
      success: true,
      message: '供应商删除成功'
    });
  } catch (error) {
    console.error('删除供应商失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: '删除供应商失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}