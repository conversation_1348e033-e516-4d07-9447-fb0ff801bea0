'use client';

import { useState, useEffect } from 'react';
import { Dialog } from '@headlessui/react';

interface Product {
  id: number;
  name: string;
  generic_name: string;
  barcode: string;
  stock_quantity: number;
  category_id: number;
  category_name: string;
  manufacturer: string;
  specification: string;
  supplier_id?: number;
  supplier_name?: string;
}

interface Category {
  id: number;
  name: string;
}

interface Supplier {
  id: number;
  name: string;
}

interface StockInFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
}

interface StockInFormData {
  product_id: number;
  quantity: number;
  supplier_id: number;
  stock_in_date: string;
  batch_number: string;
  expiry_date: string;
  cost_price: number;
  notes: string;
  trace_codes: string[];
  upload_to_mashangfangxin: boolean;
}

export default function StockInForm({ isOpen, onClose, onSubmit }: StockInFormProps) {
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [searchMethod, setSearchMethod] = useState<'category' | 'barcode' | 'name'>('name');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<number>(0);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

  const [formData, setFormData] = useState<StockInFormData>({
    product_id: 0,
    quantity: 1,
    supplier_id: 0,
    stock_in_date: new Date().toISOString().split('T')[0],
    batch_number: '',
    expiry_date: '',
    cost_price: 0,
    notes: '',
    trace_codes: [],
    upload_to_mashangfangxin: false
  });

  useEffect(() => {
    fetchProducts();
    fetchSuppliers();
    fetchCategories();
  }, []);

  // 药品搜索和筛选
  useEffect(() => {
    let filtered = products;

    if (searchMethod === 'category' && selectedCategory > 0) {
      filtered = products.filter(product => product.category_id === selectedCategory);
    } else if (searchMethod === 'barcode' && searchTerm.trim()) {
      filtered = products.filter(product =>
        product.barcode && product.barcode.includes(searchTerm.trim())
      );
    } else if (searchMethod === 'name' && searchTerm.trim()) {
      const term = searchTerm.trim().toLowerCase();
      filtered = products.filter(product =>
        product.name.toLowerCase().includes(term) ||
        product.generic_name?.toLowerCase().includes(term) ||
        product.manufacturer?.toLowerCase().includes(term)
      );
    }

    setFilteredProducts(filtered);
  }, [products, searchMethod, searchTerm, selectedCategory]);

  // 重置表单
  useEffect(() => {
    if (isOpen) {
      setSearchMethod('name');
      setSearchTerm('');
      setSelectedCategory(0);
      setSelectedProduct(null);
      setFormData({
        product_id: 0,
        quantity: 1,
        supplier_id: 0,
        stock_in_date: new Date().toISOString().split('T')[0],
        batch_number: '',
        expiry_date: '',
        cost_price: 0,
        notes: ''
      });
    }
  }, [isOpen]);

  const fetchProducts = async () => {
    try {
      const response = await fetch('/api/products');
      const data = await response.json();
      if (data.success) {
        setProducts(data.data);
      }
    } catch (error) {
      console.error('获取药品数据失败:', error);
    }
  };

  const fetchSuppliers = async () => {
    try {
      const response = await fetch('/api/suppliers');
      const data = await response.json();
      if (data.success) {
        setSuppliers(data.data);
      }
    } catch (error) {
      console.error('获取供应商数据失败:', error);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories');
      const data = await response.json();
      if (data.success) {
        setCategories(data.data);
      }
    } catch (error) {
      console.error('获取药品分类数据失败:', error);
    }
  };

  // 处理搜索方式变化
  const handleSearchMethodChange = (method: 'category' | 'barcode' | 'name') => {
    setSearchMethod(method);
    setSearchTerm('');
    setSelectedCategory(0);
    setSelectedProduct(null);
    setFormData(prev => ({ ...prev, product_id: 0 }));
  };

  // 处理药品选择
  const handleProductSelect = (productId: number) => {
    const product = filteredProducts.find(p => p.id === productId);
    setSelectedProduct(product || null);
    setFormData(prev => ({
      ...prev,
      product_id: productId,
      supplier_id: product?.supplier_id || 0
    }));
  };

  // 处理条形码扫描
  const handleBarcodeSearch = (barcode: string) => {
    const product = products.find(p => p.barcode === barcode);
    if (product) {
      setSelectedProduct(product);
      setFormData(prev => ({
        ...prev,
        product_id: product.id,
        supplier_id: product.supplier_id || 0
      }));
      setSearchTerm(barcode);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    if (name === 'quantity' || name === 'cost_price') {
      setFormData(prev => ({
        ...prev,
        [name]: parseFloat(value) || 0
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.product_id) {
      alert('请选择要入库的药品');
      return;
    }

    if (!formData.supplier_id) {
      alert('请选择供应商');
      return;
    }

    if (formData.quantity <= 0) {
      alert('入库数量必须大于0');
      return;
    }

    onSubmit({
      ...formData,
      product_name: selectedProduct?.name,
      supplier_name: suppliers.find(s => s.id === formData.supplier_id)?.name
    });
  };

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="mx-auto max-w-4xl w-full bg-white rounded-xl shadow-lg max-h-[90vh] overflow-y-auto">
          <div className="p-6">
            <Dialog.Title className="text-xl font-bold text-blue-700 mb-6">
              药品入库
            </Dialog.Title>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* 药品搜索区域 */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-blue-700 mb-4">选择药品</h3>

                {/* 搜索方式选择 */}
                <div className="flex space-x-4 mb-4">
                  <button
                    type="button"
                    onClick={() => handleSearchMethodChange('name')}
                    className={`px-4 py-2 rounded-md text-sm font-medium ${
                      searchMethod === 'name'
                        ? 'bg-blue-600 text-white'
                        : 'bg-white text-gray-700 border border-gray-300'
                    }`}
                  >
                    按名称搜索
                  </button>
                  <button
                    type="button"
                    onClick={() => handleSearchMethodChange('barcode')}
                    className={`px-4 py-2 rounded-md text-sm font-medium ${
                      searchMethod === 'barcode'
                        ? 'bg-blue-600 text-white'
                        : 'bg-white text-gray-700 border border-gray-300'
                    }`}
                  >
                    按条形码搜索
                  </button>
                  <button
                    type="button"
                    onClick={() => handleSearchMethodChange('category')}
                    className={`px-4 py-2 rounded-md text-sm font-medium ${
                      searchMethod === 'category'
                        ? 'bg-blue-600 text-white'
                        : 'bg-white text-gray-700 border border-gray-300'
                    }`}
                  >
                    按分类筛选
                  </button>
                </div>

                {/* 搜索输入区域 */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  {searchMethod === 'category' ? (
                    <div>
                      <label className="block text-sm font-medium text-blue-700 mb-1">
                        选择药品分类
                      </label>
                      <select
                        value={selectedCategory}
                        onChange={(e) => setSelectedCategory(parseInt(e.target.value) || 0)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                      >
                        <option value={0}>请选择分类</option>
                        {categories.map(category => (
                          <option key={category.id} value={category.id}>{category.name}</option>
                        ))}
                      </select>
                    </div>
                  ) : (
                    <div>
                      <label className="block text-sm font-medium text-blue-700 mb-1">
                        {searchMethod === 'barcode' ? '输入或扫描条形码' : '输入药品名称/通用名/厂家'}
                      </label>
                      <input
                        type="text"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        placeholder={
                          searchMethod === 'barcode'
                            ? '请输入或扫描条形码'
                            : '请输入药品名称、通用名或生产厂家'
                        }
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                      />
                    </div>
                  )}

                  <div>
                    <label className="block text-sm font-medium text-blue-700 mb-1">
                      选择药品
                    </label>
                    <select
                      value={formData.product_id}
                      onChange={(e) => handleProductSelect(parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                      required
                    >
                      <option value={0}>请选择药品</option>
                      {filteredProducts.map(product => (
                        <option key={product.id} value={product.id}>
                          {product.name} - {product.specification} ({product.manufacturer})
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* 选中药品信息显示 */}
                {selectedProduct && (
                  <div className="bg-blue-50 p-3 rounded-md">
                    <h4 className="font-medium text-blue-700 mb-2">已选择药品信息：</h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm text-blue-600">
                      <div><strong>药品名称：</strong>{selectedProduct.name}</div>
                      <div><strong>通用名：</strong>{selectedProduct.generic_name}</div>
                      <div><strong>规格：</strong>{selectedProduct.specification}</div>
                      <div><strong>生产厂家：</strong>{selectedProduct.manufacturer}</div>
                      <div><strong>分类：</strong>{selectedProduct.category_name}</div>
                      <div><strong>条形码：</strong>{selectedProduct.barcode}</div>
                      <div><strong>当前库存：</strong>{selectedProduct.stock_quantity}</div>
                      {selectedProduct.supplier_name && (
                        <div><strong>默认供应商：</strong>{selectedProduct.supplier_name}</div>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* 入库信息区域 */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-blue-700 mb-4">入库信息</h3>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-blue-700 mb-1">
                      入库数量 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="number"
                      name="quantity"
                      value={formData.quantity}
                      onChange={handleChange}
                      min="1"
                      step="1"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-blue-700 mb-1">
                      入库日期 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="date"
                      name="stock_in_date"
                      value={formData.stock_in_date}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-blue-700 mb-1">
                      进货成本价
                    </label>
                    <input
                      type="number"
                      name="cost_price"
                      value={formData.cost_price}
                      onChange={handleChange}
                      min="0"
                      step="0.01"
                      placeholder="0.00"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-blue-700 mb-1">
                      供应商 <span className="text-red-500">*</span>
                    </label>
                    <select
                      name="supplier_id"
                      value={formData.supplier_id}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                      required
                    >
                      <option value={0}>请选择供应商</option>
                      {suppliers.map(supplier => (
                        <option key={supplier.id} value={supplier.id}>{supplier.name}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-blue-700 mb-1">
                      批次号
                    </label>
                    <input
                      type="text"
                      name="batch_number"
                      value={formData.batch_number}
                      onChange={handleChange}
                      placeholder="请输入批次号"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-blue-700 mb-1">
                      有效期
                    </label>
                    <input
                      type="date"
                      name="expiry_date"
                      value={formData.expiry_date}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                    />
                  </div>
                </div>

                <div className="mt-4">
                  <label className="block text-sm font-medium text-blue-700 mb-1">
                    备注
                  </label>
                  <textarea
                    name="notes"
                    value={formData.notes}
                    onChange={handleChange}
                    rows={3}
                    placeholder="请输入入库备注信息..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                  />
                </div>
              </div>

              {/* 按钮区域 */}
              <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-6 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors"
                >
                  取消
                </button>
                <button
                  type="submit"
                  disabled={!formData.product_id || !formData.supplier_id || formData.quantity <= 0}
                  className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  确认入库
                </button>
              </div>
            </form>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
}